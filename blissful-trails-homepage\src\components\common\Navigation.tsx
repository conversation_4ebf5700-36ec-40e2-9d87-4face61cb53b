'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { SITE_CONFIG, NAVIGATION_LINKS, CONTACT_INFO } from '@/lib/constants';
import { useScrollTo } from '@/lib/hooks';
import EnhancedIcon from '@/components/ui/EnhancedIcon';

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { scrollToElement } = useScrollTo();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = (href: string) => {
    if (href.startsWith('#')) {
      scrollToElement(href.substring(1));
    } else {
      window.location.href = href;
    }
    setIsOpen(false);
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled 
        ? 'bg-dark-900/95 backdrop-blur-md shadow-lg' 
        : 'bg-dark-900/80 backdrop-blur-sm'
    }`}>
      <div className="container-custom">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo/Brand */}
          <motion.div
            className="flex items-center space-x-3"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-xl">🏔️</span>
            </div>
            <span className="text-white font-heading font-bold text-xl lg:text-2xl">
              {SITE_CONFIG.name}
            </span>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {NAVIGATION_LINKS.map((link, index) => (
              <motion.button
                key={link.label}
                onClick={() => handleNavClick(link.href)}
                className="text-white/90 hover:text-white font-medium transition-colors duration-200 relative group"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                {link.label}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full" />
              </motion.button>
            ))}
          </div>

          {/* Desktop Contact Info */}
          <div className="hidden lg:flex items-center space-x-4">
            <motion.a
              href={`tel:${CONTACT_INFO.phone}`}
              className="flex items-center space-x-2 text-white/90 hover:text-white transition-colors duration-200 bg-white/10 px-3 py-2 rounded-lg hover:bg-white/20"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <EnhancedIcon name="Phone" size="sm" className="text-white/90" />
              <span className="text-sm font-medium">{CONTACT_INFO.phone}</span>
            </motion.a>
            
            <motion.button
              onClick={() => scrollToElement('booking-form')}
              className="btn-primary text-sm px-4 py-2"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              Book Now
            </motion.button>
          </div>

          {/* Mobile Menu Button */}
          <motion.button
            onClick={() => setIsOpen(!isOpen)}
            className="lg:hidden text-white p-2 hover:bg-white/10 rounded-lg transition-colors duration-200"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <EnhancedIcon
              name={isOpen ? "X" : "Menu"}
              size="lg"
              className="text-white"
            />
          </motion.button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="lg:hidden bg-dark-900/98 backdrop-blur-md border-t border-white/10"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="container-custom py-6">
              {/* Mobile Navigation Links */}
              <div className="space-y-4 mb-6">
                {NAVIGATION_LINKS.map((link, index) => (
                  <motion.button
                    key={link.label}
                    onClick={() => handleNavClick(link.href)}
                    className="block w-full text-left text-white/90 hover:text-white font-medium py-2 transition-colors duration-200"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    {link.label}
                  </motion.button>
                ))}
              </div>

              {/* Mobile Contact Info */}
              <div className="space-y-4 pt-4 border-t border-white/10">
                <motion.a
                  href={`tel:${CONTACT_INFO.phone}`}
                  className="flex items-center space-x-3 text-white/90 hover:text-white transition-colors duration-200 bg-white/5 p-3 rounded-lg hover:bg-white/10"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 }}
                >
                  <EnhancedIcon name="Phone" size="md" className="text-white/90" />
                  <span className="font-medium">{CONTACT_INFO.phone}</span>
                </motion.a>

                <motion.a
                  href={`mailto:${CONTACT_INFO.email}`}
                  className="flex items-center space-x-3 text-white/90 hover:text-white transition-colors duration-200 bg-white/5 p-3 rounded-lg hover:bg-white/10"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                >
                  <EnhancedIcon name="Mail" size="md" className="text-white/90" />
                  <span className="font-medium">{CONTACT_INFO.email}</span>
                </motion.a>
                
                <motion.button
                  onClick={() => {
                    scrollToElement('booking-form');
                    setIsOpen(false);
                  }}
                  className="btn-primary w-full py-3 mt-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                >
                  Book Your Trip Now
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
}
