'use client';

import { motion } from 'framer-motion';
import { 
  Shield<PERSON>heck, 
  Phone, 
  UserCheck, 
  Heart, 
  Shield, 
  MapPin, 
  Award, 
  Plane, 
  Building, 
  Users, 
  Clock, 
  CheckCircle 
} from 'lucide-react';
import { TRUST_SAFETY_CONTENT } from '@/data/content';

const iconMap = {
  ShieldCheck,
  Phone,
  UserCheck,
  Heart,
  Shield,
  MapPin,
  Award,
  Plane,
  Building,
  Users,
  Clock,
  CheckCircle
};

export default function TrustSafetySection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 mb-4">
            {TRUST_SAFETY_CONTENT.headline}
          </h2>
          <p className="text-xl text-primary-600 mb-6 font-medium">
            {TRUST_SAFETY_CONTENT.subheadline}
          </p>
          <p className="text-lg text-neutral-600 max-w-3xl mx-auto">
            {TRUST_SAFETY_CONTENT.description}
          </p>
        </motion.div>

        {/* Safety Features Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {TRUST_SAFETY_CONTENT.features.map((feature) => {
            const IconComponent = iconMap[feature.icon as keyof typeof iconMap];

            return (
              <motion.div
                key={feature.id}
                className="bg-neutral-50 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group"
                variants={itemVariants}
              >
                <div className="flex items-start gap-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center group-hover:bg-primary-200 transition-colors duration-300">
                    <IconComponent className="w-6 h-6 text-primary-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold text-neutral-900">
                        {feature.title}
                      </h3>
                      <span className="bg-secondary-100 text-secondary-700 text-xs px-2 py-1 rounded-full font-medium">
                        {feature.badge}
                      </span>
                    </div>
                    <p className="text-neutral-600 text-sm mb-3">
                      {feature.description}
                    </p>
                  </div>
                </div>

                {/* Feature Details */}
                <ul className="space-y-2">
                  {feature.details.map((detail, detailIndex) => (
                    <li key={detailIndex} className="flex items-center gap-2 text-sm text-neutral-600">
                      <div className="w-1.5 h-1.5 bg-primary-500 rounded-full flex-shrink-0" />
                      {detail}
                    </li>
                  ))}
                </ul>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {TRUST_SAFETY_CONTENT.stats.map((stat, index) => {
              const IconComponent = iconMap[stat.icon as keyof typeof iconMap];

              return (
                <motion.div
                  key={index}
                  className="text-center text-white"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <IconComponent className="w-8 h-8 mx-auto mb-2 opacity-80" />
                  <div className="text-2xl md:text-3xl font-bold mb-1">
                    {stat.number}
                  </div>
                  <div className="text-sm opacity-90">
                    {stat.label}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Certifications */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <h3 className="text-xl font-semibold text-neutral-900 mb-8">
            Certified & Recognized By
          </h3>
          <div className="flex flex-wrap justify-center items-center gap-8 mb-8">
            {TRUST_SAFETY_CONTENT.certifications.map((cert, index) => {
              const IconComponent = iconMap[cert.icon as keyof typeof iconMap];

              return (
                <motion.div
                  key={index}
                  className="flex items-center gap-3 bg-neutral-50 px-6 py-3 rounded-lg"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <IconComponent className="w-6 h-6 text-primary-600" />
                  <div className="text-left">
                    <div className="font-semibold text-neutral-900 text-sm">
                      {cert.name}
                    </div>
                    <div className="text-xs text-neutral-600">
                      {cert.description}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Trust Message */}
          <p className="text-lg text-neutral-600 italic mb-8 max-w-2xl mx-auto">
            &ldquo;{TRUST_SAFETY_CONTENT.trustMessage}&rdquo;
          </p>

          {/* CTA Button */}
          <motion.button
            className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {TRUST_SAFETY_CONTENT.ctaText}
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
