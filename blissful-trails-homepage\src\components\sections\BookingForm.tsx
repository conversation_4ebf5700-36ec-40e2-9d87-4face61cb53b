'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MapPin, 
  Calendar, 
  User, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Users,
  Mountain,
  Building,
  Leaf,
  Camera,
  Binoculars,
  Phone,

  MessageCircle
} from 'lucide-react';
import { BOOKING_FORM_CONTENT } from '@/data/content';
import { CONTACT_INFO } from '@/lib/constants';
import { useExternalLinks } from '@/lib/hooks';

const iconMap = {
  MapPin,
  Calendar,
  User,
  CheckCircle,
  Users,
  Mountain,
  Building,
  Leaf,
  Camera,
  Binoculars
};

interface FormData {
  destinations: string[];
  travelStyle: string;
  startDate: string;
  duration: string;
  groupSize: number;
  budget: string;
  accommodation: string;
  specialRequests: string;
  name: string;
  email: string;
  phone: string;
  city: string;
}

interface FormErrors {
  destinations?: string;
  travelStyle?: string;
  startDate?: string;
  duration?: string;
  groupSize?: string;
  budget?: string;
  accommodation?: string;
  specialRequests?: string;
  name?: string;
  email?: string;
  phone?: string;
  city?: string;
}

export default function BookingForm() {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { openWhatsApp } = useExternalLinks();
  const [formData, setFormData] = useState<FormData>({
    destinations: [],
    travelStyle: '',
    startDate: '',
    duration: '',
    groupSize: 1,
    budget: '',
    accommodation: '',
    specialRequests: '',
    name: '',
    email: '',
    phone: '',
    city: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});

  const updateFormData = (field: keyof FormData, value: string | number | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: FormErrors = {};

    switch (step) {
      case 0: // Destination step
        if (formData.destinations.length === 0) {
          newErrors.destinations = 'Please select at least one destination';
        }
        if (!formData.travelStyle) {
          newErrors.travelStyle = 'Please select a travel style';
        }
        break;
      case 1: // Details step
        if (!formData.startDate) {
          newErrors.startDate = 'Please select a travel date';
        } else {
          const selectedDate = new Date(formData.startDate);
          const minDate = new Date();
          minDate.setDate(minDate.getDate() + 7);
          if (selectedDate < minDate) {
            newErrors.startDate = 'Travel date must be at least 7 days from today';
          }
        }
        if (!formData.duration) {
          newErrors.duration = 'Please select trip duration';
        }
        if (!formData.groupSize || formData.groupSize < 1) {
          newErrors.groupSize = 'Please enter a valid group size';
        }
        if (!formData.budget) {
          newErrors.budget = 'Please select a budget range';
        }
        break;
      case 2: // Contact step
        if (!formData.name.trim()) {
          newErrors.name = 'Please enter your full name';
        }
        if (!formData.email.trim()) {
          newErrors.email = 'Please enter your email address';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
          newErrors.email = 'Please enter a valid email address';
        }
        if (!formData.phone.trim()) {
          newErrors.phone = 'Please enter your phone number';
        } else if (!/^\+?[\d\s-()]{10,}$/.test(formData.phone)) {
          newErrors.phone = 'Please enter a valid phone number';
        }
        if (!formData.city.trim()) {
          newErrors.city = 'Please enter your city';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, BOOKING_FORM_CONTENT.steps.length - 1));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
      
      // Send WhatsApp message
      const message = `New booking request from ${formData.name}:\n\nDestinations: ${formData.destinations.join(', ')}\nTravel Style: ${formData.travelStyle}\nDates: ${formData.startDate}\nDuration: ${formData.duration}\nGroup Size: ${formData.groupSize}\nBudget: ${formData.budget}\n\nContact: ${formData.phone}\nEmail: ${formData.email}`;
      openWhatsApp(CONTACT_INFO.whatsapp, message);
    }, 2000);
  };

  const handleDestinationToggle = (destinationId: string) => {
    const newDestinations = formData.destinations.includes(destinationId)
      ? formData.destinations.filter(id => id !== destinationId)
      : [...formData.destinations, destinationId];
    updateFormData('destinations', newDestinations);
  };

  if (isSubmitted) {
    return (
      <section id="booking-form-success" className="py-20 bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="container mx-auto px-4">
          <motion.div
            className="max-w-2xl mx-auto text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-10 h-10 text-white" />
            </div>
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 mb-4">
              {BOOKING_FORM_CONTENT.successMessage.headline}
            </h2>
            <p className="text-lg text-neutral-600 mb-8">
              {BOOKING_FORM_CONTENT.successMessage.message}
            </p>
            
            <div className="bg-white rounded-xl p-6 shadow-sm mb-8">
              <h3 className="text-xl font-semibold mb-4">What happens next?</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {BOOKING_FORM_CONTENT.successMessage.nextSteps.map((step, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-primary-600 text-sm font-semibold">{index + 1}</span>
                    </div>
                    <span className="text-neutral-700">{step}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                type="button"
                onClick={() => {
                  openWhatsApp(CONTACT_INFO.whatsapp, 'Hi! I just submitted a booking request and have some questions.');
                }}
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-300 flex items-center justify-center gap-2"
              >
                <MessageCircle className="w-5 h-5" />
                Chat on WhatsApp
              </button>
              <button
                type="button"
                onClick={() => window.location.reload()}
                className="border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-300"
              >
                Plan Another Trip
              </button>
            </div>
          </motion.div>
        </div>
      </section>
    );
  }

  return (
    <section id="booking-form" className="py-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 mb-4">
              {BOOKING_FORM_CONTENT.headline}
            </h2>
            <p className="text-xl text-neutral-600 mb-2">
              {BOOKING_FORM_CONTENT.subheadline}
            </p>
            <p className="text-lg text-neutral-500">
              {BOOKING_FORM_CONTENT.description}
            </p>
          </motion.div>

          {/* Progress Steps */}
          <div className="flex justify-center mb-12">
            <div className="flex items-center gap-4">
              {BOOKING_FORM_CONTENT.steps.map((step, index) => {
                const IconComponent = iconMap[step.icon as keyof typeof iconMap];
                const isActive = index === currentStep;
                const isCompleted = index < currentStep;
                
                return (
                  <div key={step.id} className="flex items-center">
                    <div className={`
                      w-12 h-12 rounded-full flex items-center justify-center transition-colors duration-300
                      ${isActive ? 'bg-primary-600 text-white' : 
                        isCompleted ? 'bg-green-500 text-white' : 'bg-neutral-200 text-neutral-500'}
                    `}>
                      {isCompleted ? (
                        <CheckCircle className="w-6 h-6" />
                      ) : (
                        <IconComponent className="w-6 h-6" />
                      )}
                    </div>
                    {index < BOOKING_FORM_CONTENT.steps.length - 1 && (
                      <div className={`
                        w-16 h-1 mx-2 transition-colors duration-300
                        ${isCompleted ? 'bg-green-500' : 'bg-neutral-200'}
                      `} />
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Form Content */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {/* Step 0: Destination Selection */}
                {currentStep === 0 && (
                  <div>
                    <h3 className="text-2xl font-semibold mb-2 text-center">
                      {BOOKING_FORM_CONTENT.steps[currentStep].title}
                    </h3>
                    <p className="text-neutral-600 mb-8 text-center">
                      {BOOKING_FORM_CONTENT.steps[currentStep].description}
                    </p>

                    {/* Destinations Grid */}
                    <div className="mb-8">
                      <label className="block text-lg font-semibold mb-4">Select Destinations</label>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {BOOKING_FORM_CONTENT.destinations.map((destination) => (
                          <div
                            key={destination.id}
                            onClick={() => handleDestinationToggle(destination.id)}
                            className={`
                              cursor-pointer border-2 rounded-xl p-4 transition-all duration-300
                              ${formData.destinations.includes(destination.id)
                                ? 'border-primary-500 bg-primary-50'
                                : 'border-neutral-200 hover:border-primary-300'
                              }
                            `}
                          >
                            <h4 className="font-semibold mb-2">{destination.name}</h4>
                            <p className="text-sm text-neutral-600">{destination.description}</p>
                          </div>
                        ))}
                      </div>
                      {errors.destinations && (
                        <p className="text-red-500 text-sm mt-2">{errors.destinations}</p>
                      )}
                    </div>

                    {/* Travel Style */}
                    <div>
                      <label className="block text-lg font-semibold mb-4">Travel Style</label>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {BOOKING_FORM_CONTENT.travelStyles.map((style) => {
                          const IconComponent = iconMap[style.icon as keyof typeof iconMap];
                          return (
                            <div
                              key={style.id}
                              onClick={() => updateFormData('travelStyle', style.id)}
                              className={`
                                cursor-pointer border-2 rounded-xl p-4 transition-all duration-300 flex items-start gap-3
                                ${formData.travelStyle === style.id
                                  ? 'border-primary-500 bg-primary-50'
                                  : 'border-neutral-200 hover:border-primary-300'
                                }
                              `}
                            >
                              <IconComponent className="w-6 h-6 text-primary-600 flex-shrink-0 mt-1" />
                              <div>
                                <h4 className="font-semibold mb-1">{style.name}</h4>
                                <p className="text-sm text-neutral-600">{style.description}</p>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      {errors.travelStyle && (
                        <p className="text-red-500 text-sm mt-2">{errors.travelStyle}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Step 1: Trip Details */}
                {currentStep === 1 && (
                  <div>
                    <h3 className="text-2xl font-semibold mb-2 text-center">
                      {BOOKING_FORM_CONTENT.steps[currentStep].title}
                    </h3>
                    <p className="text-neutral-600 mb-8 text-center">
                      {BOOKING_FORM_CONTENT.steps[currentStep].description}
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Start Date */}
                      <div>
                        <label className="block text-sm font-semibold mb-2">Travel Start Date</label>
                        <input
                          type="date"
                          value={formData.startDate}
                          onChange={(e) => updateFormData('startDate', e.target.value)}
                          min={new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
                          className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:border-primary-500"
                          aria-label="Travel Start Date"
                        />
                        {errors.startDate && (
                          <p className="text-red-500 text-sm mt-1">{errors.startDate}</p>
                        )}
                      </div>

                      {/* Duration */}
                      <div>
                        <label className="block text-sm font-semibold mb-2">Trip Duration</label>
                        <select
                          value={formData.duration}
                          onChange={(e) => updateFormData('duration', e.target.value)}
                          className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:border-primary-500"
                          aria-label="Trip Duration"
                        >
                          <option value="">Select duration</option>
                          {BOOKING_FORM_CONTENT.formFields.duration.options?.map((option) => (
                            <option key={option} value={option}>{option}</option>
                          ))}
                        </select>
                        {errors.duration && (
                          <p className="text-red-500 text-sm mt-1">{errors.duration}</p>
                        )}
                      </div>

                      {/* Group Size */}
                      <div>
                        <label className="block text-sm font-semibold mb-2">Group Size</label>
                        <input
                          type="number"
                          min="1"
                          max="20"
                          value={formData.groupSize}
                          onChange={(e) => updateFormData('groupSize', parseInt(e.target.value) || 1)}
                          className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:border-primary-500"
                          aria-label="Group Size"
                          placeholder="Number of travelers"
                        />
                        {errors.groupSize && (
                          <p className="text-red-500 text-sm mt-1">{errors.groupSize}</p>
                        )}
                      </div>

                      {/* Accommodation */}
                      <div>
                        <label className="block text-sm font-semibold mb-2">Accommodation Preference</label>
                        <select
                          value={formData.accommodation}
                          onChange={(e) => updateFormData('accommodation', e.target.value)}
                          className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:border-primary-500"
                          aria-label="Accommodation Preference"
                        >
                          <option value="">Select accommodation type</option>
                          {BOOKING_FORM_CONTENT.accommodationTypes.map((type) => (
                            <option key={type.id} value={type.id}>{type.name}</option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {/* Budget Range */}
                    <div className="mt-6">
                      <label className="block text-lg font-semibold mb-4">Budget Range</label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {BOOKING_FORM_CONTENT.budgetRanges.map((budget) => (
                          <div
                            key={budget.id}
                            onClick={() => updateFormData('budget', budget.id)}
                            className={`
                              cursor-pointer border-2 rounded-xl p-4 transition-all duration-300
                              ${formData.budget === budget.id
                                ? 'border-primary-500 bg-primary-50'
                                : 'border-neutral-200 hover:border-primary-300'
                              }
                            `}
                          >
                            <h4 className="font-semibold mb-1">{budget.name}</h4>
                            <p className="text-primary-600 font-medium mb-1">{budget.range}</p>
                            <p className="text-sm text-neutral-600">{budget.description}</p>
                          </div>
                        ))}
                      </div>
                      {errors.budget && (
                        <p className="text-red-500 text-sm mt-2">{errors.budget}</p>
                      )}
                    </div>

                    {/* Special Requests */}
                    <div className="mt-6">
                      <label className="block text-sm font-semibold mb-2">Special Requirements (Optional)</label>
                      <textarea
                        value={formData.specialRequests}
                        onChange={(e) => updateFormData('specialRequests', e.target.value)}
                        placeholder="Any specific needs or preferences? (dietary, accessibility, etc.)"
                        rows={3}
                        className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:border-primary-500"
                      />
                    </div>
                  </div>
                )}

                {/* Step 2: Contact Information */}
                {currentStep === 2 && (
                  <div>
                    <h3 className="text-2xl font-semibold mb-2 text-center">
                      {BOOKING_FORM_CONTENT.steps[currentStep].title}
                    </h3>
                    <p className="text-neutral-600 mb-8 text-center">
                      {BOOKING_FORM_CONTENT.steps[currentStep].description}
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Full Name */}
                      <div>
                        <label className="block text-sm font-semibold mb-2">Full Name *</label>
                        <input
                          type="text"
                          value={formData.name}
                          onChange={(e) => updateFormData('name', e.target.value)}
                          placeholder="Enter your full name"
                          className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:border-primary-500"
                          aria-label="Full Name"
                        />
                        {errors.name && (
                          <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                        )}
                      </div>

                      {/* Email */}
                      <div>
                        <label className="block text-sm font-semibold mb-2">Email Address *</label>
                        <input
                          type="email"
                          value={formData.email}
                          onChange={(e) => updateFormData('email', e.target.value)}
                          placeholder="Enter your email address"
                          className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:border-primary-500"
                          aria-label="Email Address"
                        />
                        {errors.email && (
                          <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                        )}
                      </div>

                      {/* Phone */}
                      <div>
                        <label className="block text-sm font-semibold mb-2">Phone Number *</label>
                        <input
                          type="tel"
                          value={formData.phone}
                          onChange={(e) => updateFormData('phone', e.target.value)}
                          placeholder="Enter your phone number"
                          className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:border-primary-500"
                          aria-label="Phone Number"
                        />
                        {errors.phone && (
                          <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                        )}
                      </div>

                      {/* City */}
                      <div>
                        <label className="block text-sm font-semibold mb-2">City *</label>
                        <input
                          type="text"
                          value={formData.city}
                          onChange={(e) => updateFormData('city', e.target.value)}
                          placeholder="Your current city"
                          className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:outline-none focus:border-primary-500"
                          aria-label="City"
                        />
                        {errors.city && (
                          <p className="text-red-500 text-sm mt-1">{errors.city}</p>
                        )}
                      </div>
                    </div>

                    <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-start gap-3">
                        <Phone className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                        <div>
                          <h4 className="font-semibold text-blue-900 mb-1">Quick Response Guarantee</h4>
                          <p className="text-blue-800 text-sm">
                            Our travel experts will contact you within 2 hours to discuss your requirements and provide a personalized itinerary.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 3: Confirmation */}
                {currentStep === 3 && (
                  <div>
                    <h3 className="text-2xl font-semibold mb-2 text-center">
                      {BOOKING_FORM_CONTENT.steps[currentStep].title}
                    </h3>
                    <p className="text-neutral-600 mb-8 text-center">
                      {BOOKING_FORM_CONTENT.steps[currentStep].description}
                    </p>

                    {/* Summary */}
                    <div className="bg-neutral-50 rounded-xl p-6 mb-6">
                      <h4 className="text-lg font-semibold mb-4">Trip Summary</h4>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-neutral-600">Destinations:</span>
                          <p className="mt-1">
                            {formData.destinations.map(id =>
                              BOOKING_FORM_CONTENT.destinations.find(d => d.id === id)?.name
                            ).join(', ')}
                          </p>
                        </div>

                        <div>
                          <span className="font-medium text-neutral-600">Travel Style:</span>
                          <p className="mt-1">
                            {BOOKING_FORM_CONTENT.travelStyles.find(s => s.id === formData.travelStyle)?.name}
                          </p>
                        </div>

                        <div>
                          <span className="font-medium text-neutral-600">Travel Date:</span>
                          <p className="mt-1">{formData.startDate}</p>
                        </div>

                        <div>
                          <span className="font-medium text-neutral-600">Duration:</span>
                          <p className="mt-1">{formData.duration}</p>
                        </div>

                        <div>
                          <span className="font-medium text-neutral-600">Group Size:</span>
                          <p className="mt-1">{formData.groupSize} travelers</p>
                        </div>

                        <div>
                          <span className="font-medium text-neutral-600">Budget:</span>
                          <p className="mt-1">
                            {BOOKING_FORM_CONTENT.budgetRanges.find(b => b.id === formData.budget)?.name}
                          </p>
                        </div>
                      </div>

                      {formData.specialRequests && (
                        <div className="mt-4 pt-4 border-t border-neutral-200">
                          <span className="font-medium text-neutral-600">Special Requirements:</span>
                          <p className="mt-1 text-sm">{formData.specialRequests}</p>
                        </div>
                      )}
                    </div>

                    {/* Contact Summary */}
                    <div className="bg-primary-50 rounded-xl p-6 mb-6">
                      <h4 className="text-lg font-semibold mb-4">Contact Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-neutral-600">Name:</span>
                          <p className="mt-1">{formData.name}</p>
                        </div>
                        <div>
                          <span className="font-medium text-neutral-600">Email:</span>
                          <p className="mt-1">{formData.email}</p>
                        </div>
                        <div>
                          <span className="font-medium text-neutral-600">Phone:</span>
                          <p className="mt-1">{formData.phone}</p>
                        </div>
                        <div>
                          <span className="font-medium text-neutral-600">City:</span>
                          <p className="mt-1">{formData.city}</p>
                        </div>
                      </div>
                    </div>

                    {/* Next Steps */}
                    <div className="bg-green-50 rounded-xl p-6">
                      <h4 className="text-lg font-semibold mb-4 text-green-900">What happens next?</h4>
                      <div className="space-y-3">
                        {BOOKING_FORM_CONTENT.successMessage.nextSteps.map((step, index) => (
                          <div key={index} className="flex items-center gap-3">
                            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                              <span className="text-white text-sm font-semibold">{index + 1}</span>
                            </div>
                            <span className="text-green-800">{step}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <button
                type="button"
                onClick={prevStep}
                disabled={currentStep === 0}
                className="flex items-center gap-2 px-6 py-3 border-2 border-neutral-300 text-neutral-600 rounded-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:border-neutral-400 transition-colors duration-300"
              >
                <ArrowLeft className="w-5 h-5" />
                Previous
              </button>

              {currentStep === BOOKING_FORM_CONTENT.steps.length - 1 ? (
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="flex items-center gap-2 px-8 py-3 bg-primary-600 hover:bg-primary-700 disabled:opacity-50 text-white rounded-lg font-semibold transition-colors duration-300"
                >
                  {isSubmitting ? 'Submitting...' : BOOKING_FORM_CONTENT.submitButton.text}
                  {!isSubmitting && <ArrowRight className="w-5 h-5" />}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={nextStep}
                  className="flex items-center gap-2 px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-semibold transition-colors duration-300"
                >
                  Next
                  <ArrowRight className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>

          {/* Trust Indicators */}
          <motion.div
            className="flex flex-wrap justify-center gap-6 mt-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {BOOKING_FORM_CONTENT.trustIndicators.map((indicator, index) => (
              <div key={index} className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <span className="text-sm font-medium text-neutral-700">{indicator}</span>
              </div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
