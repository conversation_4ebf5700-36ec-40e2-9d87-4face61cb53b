'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { HERO_CONTENT } from '@/data/content';
import { useScrollTo } from '@/lib/hooks';

export default function HeroSection() {
  const [isLoading, setIsLoading] = useState(false);
  const { scrollToElement } = useScrollTo();

  const handlePrimaryCTA = async () => {
    setIsLoading(true);
    // Scroll to booking form
    scrollToElement('booking-form');
    setIsLoading(false);
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-16 lg:pt-20">
      {/* Background Image with Optimization */}
      <div className="absolute inset-0 z-0">
        {/* High-quality background image - will be replaced with actual image */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-600 via-blue-600 to-purple-700">
          {/* Simulating a temple/architecture background similar to reference */}
          <div className="absolute inset-0 opacity-30 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] bg-repeat" />
        </div>
        {/* Enhanced overlay for better text readability */}
        <div className="hero-overlay" />
      </div>

      {/* Content */}
      <div className="relative z-10 container-custom text-center text-white section-padding">
        <motion.h1
          className="text-hero-mobile md:text-hero font-heading font-bold mb-6 leading-tight max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {HERO_CONTENT.headline}
        </motion.h1>

        <motion.p
          className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto font-medium text-white/90"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {HERO_CONTENT.subheadline}
        </motion.p>

        <motion.p
          className="text-lg md:text-xl mb-12 max-w-4xl mx-auto text-white/80 leading-relaxed"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {HERO_CONTENT.description}
        </motion.p>

        {/* CTA Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-6 justify-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          data-testid="cta-container"
        >
          <button
            type="button"
            onClick={handlePrimaryCTA}
            disabled={isLoading}
            className="btn-primary text-lg px-8 py-4 min-w-[200px] transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
          >
            {isLoading ? 'Loading...' : HERO_CONTENT.primaryCTA}
          </button>

          <button
            type="button"
            onClick={() => scrollToElement('packages')}
            className="btn-ghost text-lg px-8 py-4 min-w-[200px] transform hover:scale-105 transition-all duration-300"
          >
            {HERO_CONTENT.secondaryCTA}
          </button>
        </motion.div>

        {/* Trust Badges */}
        <motion.div
          className="flex flex-wrap justify-center gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          {HERO_CONTENT.trustBadges.map((badge, index) => (
            <div key={index} className="flex items-center gap-3 bg-white/15 backdrop-blur-md px-6 py-3 rounded-full border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="w-2 h-2 bg-secondary-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-white/90">{badge}</span>
            </div>
          ))}
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
        onClick={() => scrollToElement('packages')}
      >
        <div className="w-6 h-10 border-2 border-white/60 rounded-full flex justify-center hover:border-white transition-colors duration-300">
          <div className="w-1 h-3 bg-white/80 rounded-full mt-2" />
        </div>
      </motion.div>
    </section>
  );
}
