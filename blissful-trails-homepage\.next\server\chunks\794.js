exports.id=794,exports.ids=[794],exports.modules={206:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>Y});var d=c(687),e=c(4478),f=c(9930),g=c(7992),h=c(2982),i=c(8965),j=c(9891),k=c(8038),l=c(7760),m=c(6561),n=c(1312),o=c(8869),p=c(3508),q=c(5336),r=c(334),s=c(8559),t=c(9270),u=c(4027),v=c(7033),w=c(4952),x=c(8272),y=c(3589),z=c(8340),A=c(3872),B=c(1550),C=c(8916),D=c(1361),E=c(3580),F=c(4082),G=c(9410),H=c(7840),I=c(228),J=c(8730),K=c(4398),L=c(2117),M=c(5778),N=c(9526),O=c(3851),P=c(2575),Q=c(6494),R=c(2941),S=c(1860),T=c(462);let U={Car:e.A,Plane:f.A,MapPin:g.A,Map:h.A,Mountain:i.A,Shield:j.A,ShieldCheck:k.A,Heart:l.A,Award:m.A,Users:n.A,User:o.A,UserCheck:p.A,CheckCircle:q.A,ArrowRight:r.A,ArrowLeft:s.A,Search:t.A,Settings:u.A,ChevronLeft:v.A,ChevronRight:w.A,ChevronDown:x.A,ChevronUp:y.A,Phone:z.A,MessageCircle:A.A,Mail:B.A,Quote:C.A,Camera:D.A,Binoculars:E.A,Leaf:F.A,Building:G.A,Play:H.A,Calendar:I.A,Clock:J.A,Star:K.A,ThumbsUp:L.A,CreditCard:M.A,Facebook:N.A,Instagram:O.A,Twitter:P.A,Youtube:Q.A,Menu:R.A,X:S.A,Filter:T.A},V={xs:"w-3 h-3",sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6",xl:"w-8 h-8","2xl":"w-10 h-10"},W={default:"text-neutral-600",primary:"text-primary-600",secondary:"text-secondary-600",success:"text-green-600",warning:"text-yellow-600",danger:"text-red-600"},X={default:"bg-neutral-100 text-neutral-600",primary:"bg-primary-100 text-primary-600",secondary:"bg-secondary-100 text-secondary-600",success:"bg-green-100 text-green-600",warning:"bg-yellow-100 text-yellow-600",danger:"bg-red-100 text-red-600"};function Y({name:a,size:b="md",variant:c="default",background:e=!1,className:f="","aria-label":g,...h}){let i=U[a];if(!i)return console.warn(`Icon "${a}" not found in iconMap`),null;let j=V[b],k=e?X[c]:W[c];return e?(0,d.jsx)("div",{className:`inline-flex items-center justify-center rounded-lg p-2 ${k} ${f}`,"aria-label":g,...h,children:(0,d.jsx)(i,{className:j})}):(0,d.jsx)(i,{className:`${j} ${k} ${f}`,"aria-label":g,...h})}},362:(a,b,c)=>{"use strict";c.d(b,{default:()=>k});var d=c(687),e=c(3210),f=c(1743),g=c(8920),h=c(6420),i=c(3877),j=c(206);function k(){let[a,b]=(0,e.useState)(!1),[c,k]=(0,e.useState)(!1),{scrollToElement:l}=(0,i.QV)(),m=a=>{a.startsWith("#")?l(a.substring(1)):window.location.href=a,b(!1)};return(0,d.jsxs)("nav",{className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${c?"bg-dark-900/95 backdrop-blur-md shadow-lg":"bg-dark-900/80 backdrop-blur-sm"}`,children:[(0,d.jsx)("div",{className:"container-custom",children:(0,d.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,d.jsxs)(f.P.div,{className:"flex items-center space-x-3",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6},children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,d.jsx)("span",{className:"text-white font-bold text-xl",children:"\uD83C\uDFD4️"})}),(0,d.jsx)("span",{className:"text-white font-heading font-bold text-xl lg:text-2xl",children:h.jx.name})]}),(0,d.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:h.Tl.map((a,b)=>(0,d.jsxs)(f.P.button,{onClick:()=>m(a.href),className:"text-white/90 hover:text-white font-medium transition-colors duration-200 relative group",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},children:[a.label,(0,d.jsx)("span",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full"})]},a.label))}),(0,d.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,d.jsxs)(f.P.a,{href:`tel:${h.Ux.phone}`,className:"flex items-center space-x-2 text-white/90 hover:text-white transition-colors duration-200 bg-white/10 px-3 py-2 rounded-lg hover:bg-white/20",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.4},children:[(0,d.jsx)(j.Ay,{name:"Phone",size:"sm",className:"text-white/90"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:h.Ux.phone})]}),(0,d.jsx)(f.P.button,{onClick:()=>l("booking-form"),className:"btn-primary text-sm px-4 py-2",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.5},children:"Book Now"})]}),(0,d.jsx)(f.P.button,{onClick:()=>b(!a),className:"lg:hidden text-white p-2 hover:bg-white/10 rounded-lg transition-colors duration-200",initial:{opacity:0},animate:{opacity:1},transition:{duration:.6},children:(0,d.jsx)(j.Ay,{name:a?"X":"Menu",size:"lg",className:"text-white"})})]})}),(0,d.jsx)(g.N,{children:a&&(0,d.jsx)(f.P.div,{className:"lg:hidden bg-dark-900/98 backdrop-blur-md border-t border-white/10",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,d.jsxs)("div",{className:"container-custom py-6",children:[(0,d.jsx)("div",{className:"space-y-4 mb-6",children:h.Tl.map((a,b)=>(0,d.jsx)(f.P.button,{onClick:()=>m(a.href),className:"block w-full text-left text-white/90 hover:text-white font-medium py-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*b},children:a.label},a.label))}),(0,d.jsxs)("div",{className:"space-y-4 pt-4 border-t border-white/10",children:[(0,d.jsxs)(f.P.a,{href:`tel:${h.Ux.phone}`,className:"flex items-center space-x-3 text-white/90 hover:text-white transition-colors duration-200 bg-white/5 p-3 rounded-lg hover:bg-white/10",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.4},children:[(0,d.jsx)(j.Ay,{name:"Phone",size:"md",className:"text-white/90"}),(0,d.jsx)("span",{className:"font-medium",children:h.Ux.phone})]}),(0,d.jsxs)(f.P.a,{href:`mailto:${h.Ux.email}`,className:"flex items-center space-x-3 text-white/90 hover:text-white transition-colors duration-200 bg-white/5 p-3 rounded-lg hover:bg-white/10",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.5},children:[(0,d.jsx)(j.Ay,{name:"Mail",size:"md",className:"text-white/90"}),(0,d.jsx)("span",{className:"font-medium",children:h.Ux.email})]}),(0,d.jsx)(f.P.button,{onClick:()=>{l("booking-form"),b(!1)},className:"btn-primary w-full py-3 mt-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.6},children:"Book Your Trip Now"})]})]})})})]})}},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},1135:()=>{},3877:(a,b,c)=>{"use strict";c.d(b,{DE:()=>e,QV:()=>f,u:()=>g});var d=c(3210);function e(){let[a,b]=(0,d.useState)(!1);return a}function f(){let a=e()?document:null;return{scrollToElement:(b,c="smooth")=>{if(!a)return;let d=a.getElementById(b);d&&d.scrollIntoView({behavior:c})},scrollToTop:(b="smooth")=>{a&&window.scrollTo({top:0,behavior:b})}}}function g(){let a=e()?window:null;return{openLink:(b,c="_blank")=>{a&&a.open(b,c)},openWhatsApp:(b,c="")=>{if(!a)return;let d=b.replace(/[^0-9]/g,""),e=encodeURIComponent(c),f=`https://wa.me/${d}${c?`?text=${e}`:""}`;a.open(f,"_blank")}}}},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>j});var d=c(7413),e=c(1194),f=c.n(e),g=c(8013),h=c.n(g);c(1135);var i=c(4796);let j={title:{default:"Blissful Trails - Family Travel Packages | North Bengal & Sikkim Tours",template:"%s | Blissful Trails"},description:"Experience hassle-free family travel with hygiene-verified accommodations, clean cabs, and 24/7 support. Customized tour packages for Darjeeling, Sikkim, and Dooars.",keywords:["family travel packages","Darjeeling tour packages","Sikkim family tours","North Bengal travel","hygiene verified hotels","family-friendly travel","Dooars wildlife tours","hill station packages","clean cab services","customized itinerary"]};function k({children:a}){return(0,d.jsx)("html",{lang:"en",className:`${f().variable} ${h().variable}`,children:(0,d.jsxs)("body",{className:`${f().className} antialiased`,children:[(0,d.jsx)(i.default,{}),a]})})}},4660:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},4796:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\common\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\common\\Navigation.tsx","default")},4804:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},6420:(a,b,c)=>{"use strict";c.d(b,{Tl:()=>f,Ux:()=>e,jx:()=>d});let d={name:"Blissful Trails",description:"Curated trips. Clean cabs. Cozy stays.",url:"https://blissfultrails.com",ogImage:"/og-image.jpg"},e={phone:"+91-9876543210",whatsapp:"+91-9876543210",email:"<EMAIL>"},f=[{label:"Packages",href:"#packages"},{label:"How It Works",href:"#how-it-works"},{label:"About",href:"#about"},{label:"Contact",href:"#contact"}]},6610:(a,b,c)=>{Promise.resolve().then(c.bind(c,362))},6858:(a,b,c)=>{Promise.resolve().then(c.bind(c,4796))}};