'use client';

import { 
  Car, Shield, Heart, Map, Users, Calendar, 
  MapPin, User, CheckCircle, ArrowRight, ArrowLeft,
  Mountain, Building, Leaf, Camera, Binoculars, Phone, MessageCircle,
  ShieldCheck, UserCheck, Award, Plane, Clock,
  Star, ThumbsUp, ChevronLeft, ChevronRight, Quote,
  Search, Settings, CreditCard, Play, ChevronDown, ChevronUp,
  Facebook, Instagram, Twitter, Youtube, Mail,
  Menu, X, Filter
} from 'lucide-react';

// Comprehensive icon mapping
export const iconMap = {
  // Transportation & Travel
  Car, Plane, MapPin, Map, Mountain,
  
  // Safety & Security
  Shield, ShieldCheck, Heart, Award,
  
  // People & Users
  Users, User, UserCheck,
  
  // Actions & Navigation
  CheckCircle, ArrowRight, ArrowLeft, Search, Settings,
  ChevronLeft, ChevronRight, ChevronDown, ChevronUp,
  
  // Communication
  Phone, MessageCircle, Mail, Quote,
  
  // Activities & Experiences
  Camera, Binoculars, Leaf, Building, Play,
  
  // Time & Planning
  Calendar, Clock,
  
  // Ratings & Reviews
  Star, ThumbsUp,
  
  // Payment & Booking
  CreditCard,
  
  // Social Media
  Facebook, Instagram, Twitter, Youtube,
  
  // Interface
  Menu, X, Filter
};

export type IconName = keyof typeof iconMap;

interface EnhancedIconProps {
  name: IconName;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  background?: boolean;
  className?: string;
  'aria-label'?: string;
}

const sizeMap = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8',
  '2xl': 'w-10 h-10'
};

const variantMap = {
  default: 'text-neutral-600',
  primary: 'text-primary-600',
  secondary: 'text-secondary-600',
  success: 'text-green-600',
  warning: 'text-yellow-600',
  danger: 'text-red-600'
};

const backgroundVariantMap = {
  default: 'bg-neutral-100 text-neutral-600',
  primary: 'bg-primary-100 text-primary-600',
  secondary: 'bg-secondary-100 text-secondary-600',
  success: 'bg-green-100 text-green-600',
  warning: 'bg-yellow-100 text-yellow-600',
  danger: 'bg-red-100 text-red-600'
};

export default function EnhancedIcon({ 
  name, 
  size = 'md', 
  variant = 'default', 
  background = false,
  className = '',
  'aria-label': ariaLabel,
  ...props 
}: EnhancedIconProps) {
  const IconComponent = iconMap[name];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in iconMap`);
    return null;
  }

  const sizeClass = sizeMap[size];
  const colorClass = background ? backgroundVariantMap[variant] : variantMap[variant];
  
  if (background) {
    return (
      <div 
        className={`inline-flex items-center justify-center rounded-lg p-2 ${colorClass} ${className}`}
        aria-label={ariaLabel}
        {...props}
      >
        <IconComponent className={sizeClass} />
      </div>
    );
  }

  return (
    <IconComponent 
      className={`${sizeClass} ${colorClass} ${className}`}
      aria-label={ariaLabel}
      {...props}
    />
  );
}

// Specialized icon components for common use cases
export function NavigationIcon({ name, className = '' }: { name: IconName; className?: string }) {
  return <EnhancedIcon name={name} size="md" variant="default" className={className} />;
}

export function FeatureIcon({ name, className = '' }: { name: IconName; className?: string }) {
  return <EnhancedIcon name={name} size="xl" variant="primary" background className={className} />;
}

export function ActionIcon({ name, className = '' }: { name: IconName; className?: string }) {
  return <EnhancedIcon name={name} size="sm" variant="default" className={className} />;
}

export function StatusIcon({ name, variant = 'success', className = '' }: { 
  name: IconName; 
  variant?: 'success' | 'warning' | 'danger';
  className?: string;
}) {
  return <EnhancedIcon name={name} size="sm" variant={variant} className={className} />;
}

// Travel-specific icon components
export function DestinationIcon({ className = '' }: { className?: string }) {
  return <EnhancedIcon name="MapPin" size="lg" variant="primary" background className={className} />;
}

export function SafetyIcon({ className = '' }: { className?: string }) {
  return <EnhancedIcon name="ShieldCheck" size="lg" variant="success" background className={className} />;
}

export function ComfortIcon({ className = '' }: { className?: string }) {
  return <EnhancedIcon name="Heart" size="lg" variant="danger" background className={className} />;
}

export function ExperienceIcon({ className = '' }: { className?: string }) {
  return <EnhancedIcon name="Camera" size="lg" variant="secondary" background className={className} />;
}
