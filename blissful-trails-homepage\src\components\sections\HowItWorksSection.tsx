'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Settings, CreditCard, Shield, Clock, ChevronDown, ChevronUp, Play } from 'lucide-react';
import { HOW_IT_WORKS_STEPS, HOW_IT_WORKS_CONTENT } from '@/data/content';

const iconMap = {
  Search,
  Settings,
  CreditCard,
  Shield
};

export default function HowItWorksSection() {
  const [activeStep, setActiveStep] = useState<number | null>(null);
  const [expandedStep, setExpandedStep] = useState<number | null>(null);

  const handleStepClick = (stepNumber: number) => {
    setActiveStep(stepNumber);
    setExpandedStep(expandedStep === stepNumber ? null : stepNumber);
  };

  const handleStartPlanning = () => {
    // Scroll to packages section
    document.getElementById('packages')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="how-it-works" className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-section-mobile md:text-section font-heading font-bold text-neutral-900 mb-6">
            {HOW_IT_WORKS_CONTENT.headline}
          </h2>
          <p className="text-xl md:text-2xl text-primary-600 font-semibold mb-6">
            {HOW_IT_WORKS_CONTENT.subheadline}
          </p>
          <p className="text-lg md:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
            {HOW_IT_WORKS_CONTENT.description}
          </p>
        </motion.div>

        {/* Steps Container */}
        <div className="relative max-w-6xl mx-auto">
          {/* Desktop Layout */}
          <div className="hidden lg:block">
            {/* Connecting Line */}
            <div className="absolute top-24 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-200 via-primary-400 to-primary-200 z-0" />
            
            {/* Steps Grid */}
            <div className="grid grid-cols-4 gap-8 relative z-10">
              {HOW_IT_WORKS_STEPS.map((step, index) => {
                const IconComponent = iconMap[step.icon as keyof typeof iconMap];
                const isActive = activeStep === step.step;
                
                return (
                  <motion.div
                    key={step.id}
                    className="text-center"
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.2 }}
                  >
                    {/* Step Circle */}
                    <motion.div
                      className={`relative mx-auto w-20 h-20 rounded-full flex items-center justify-center cursor-pointer transition-all duration-300 ${
                        isActive
                          ? 'bg-primary-600 shadow-lg shadow-primary-600/30 scale-110'
                          : 'bg-white border-4 border-primary-200 hover:border-primary-400 hover:shadow-md'
                      }`}
                      onClick={() => handleStepClick(step.step)}
                      whileHover={{ scale: isActive ? 1.1 : 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <IconComponent 
                        className={`w-8 h-8 ${isActive ? 'text-white' : 'text-primary-600'}`} 
                      />
                      
                      {/* Step Number Badge */}
                      <div className={`absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                        isActive ? 'bg-white text-primary-600' : 'bg-primary-600 text-white'
                      }`}>
                        {step.step}
                      </div>

                      {/* Pulse Animation for Active Step */}
                      {isActive && (
                        <motion.div
                          className="absolute inset-0 rounded-full border-2 border-primary-400"
                          animate={{ scale: [1, 1.2, 1], opacity: [0.7, 0, 0.7] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                      )}
                    </motion.div>

                    {/* Step Content */}
                    <div className="mt-6">
                      <h3 className="text-xl font-heading font-semibold text-neutral-900 mb-2">
                        {step.title}
                      </h3>
                      <p className="text-neutral-600 text-sm leading-relaxed mb-3">
                        {step.description}
                      </p>
                      
                      {/* Time Estimate */}
                      <div className="flex items-center justify-center gap-1 text-xs text-primary-600 mb-4">
                        <Clock className="w-3 h-3" />
                        <span>{step.estimatedTime}</span>
                      </div>

                      {/* Interactive Element Badge */}
                      <div className="inline-flex items-center gap-1 bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-xs font-medium">
                        <Play className="w-3 h-3" />
                        {step.interactiveElement}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="lg:hidden space-y-6">
            {HOW_IT_WORKS_STEPS.map((step, index) => {
              const IconComponent = iconMap[step.icon as keyof typeof iconMap];
              const isExpanded = expandedStep === step.step;
              
              return (
                <motion.div
                  key={step.id}
                  className="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  {/* Step Header */}
                  <button
                    type="button"
                    className="w-full p-6 flex items-center gap-4 text-left hover:bg-neutral-50 transition-colors duration-200"
                    onClick={() => handleStepClick(step.step)}
                  >
                    {/* Step Circle */}
                    <div className="flex-shrink-0 w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center relative">
                      <IconComponent className="w-6 h-6 text-white" />
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-white rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold text-primary-600">{step.step}</span>
                      </div>
                    </div>

                    {/* Step Info */}
                    <div className="flex-1">
                      <h3 className="text-lg font-heading font-semibold text-neutral-900 mb-1">
                        {step.title}
                      </h3>
                      <p className="text-sm text-neutral-600">
                        {step.description}
                      </p>
                    </div>

                    {/* Expand Icon */}
                    <div className="flex-shrink-0">
                      {isExpanded ? (
                        <ChevronUp className="w-5 h-5 text-neutral-400" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-neutral-400" />
                      )}
                    </div>
                  </button>

                  {/* Expanded Content */}
                  <AnimatePresence>
                    {isExpanded && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="border-t border-neutral-200 bg-neutral-50"
                      >
                        <div className="p-6">
                          {/* Details List */}
                          <div className="mb-4">
                            <h4 className="font-semibold text-neutral-900 mb-3">What is Included:</h4>
                            <ul className="space-y-2">
                              {step.details.map((detail, detailIndex) => (
                                <li key={detailIndex} className="flex items-start gap-2 text-sm text-neutral-600">
                                  <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mt-2 flex-shrink-0" />
                                  <span>{detail}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          {/* Time and Interactive Element */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1 text-xs text-primary-600">
                              <Clock className="w-3 h-3" />
                              <span>{step.estimatedTime}</span>
                            </div>
                            <div className="inline-flex items-center gap-1 bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-xs font-medium">
                              <Play className="w-3 h-3" />
                              {step.interactiveElement}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* Connecting Line for Mobile */}
                  {index < HOW_IT_WORKS_STEPS.length - 1 && (
                    <div className="flex justify-center">
                      <div className="w-0.5 h-8 bg-gradient-to-b from-primary-300 to-primary-100" />
                    </div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Detailed Step Information (Desktop Only) */}
        <AnimatePresence>
          {activeStep && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -30 }}
              transition={{ duration: 0.4 }}
              className="hidden lg:block mt-12 bg-white rounded-2xl shadow-lg p-8 max-w-4xl mx-auto"
            >
              {(() => {
                const step = HOW_IT_WORKS_STEPS.find(s => s.step === activeStep);
                if (!step) return null;

                return (
                  <div>
                    <div className="text-center mb-6">
                      <h3 className="text-2xl font-heading font-bold text-neutral-900 mb-2">
                        Step {step.step}: {step.title}
                      </h3>
                      <p className="text-neutral-600">
                        {step.description}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      {/* Details */}
                      <div>
                        <h4 className="font-semibold text-neutral-900 mb-4">What is Included:</h4>
                        <ul className="space-y-3">
                          {step.details.map((detail, index) => (
                            <motion.li
                              key={index}
                              className="flex items-start gap-3 text-neutral-600"
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.3, delay: index * 0.1 }}
                            >
                              <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0" />
                              <span>{detail}</span>
                            </motion.li>
                          ))}
                        </ul>
                      </div>

                      {/* Interactive Element Info */}
                      <div className="bg-primary-50 rounded-xl p-6">
                        <h4 className="font-semibold text-neutral-900 mb-3">Interactive Feature:</h4>
                        <div className="flex items-center gap-2 mb-3">
                          <Play className="w-5 h-5 text-primary-600" />
                          <span className="font-medium text-primary-700">{step.interactiveElement}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-neutral-600">
                          <Clock className="w-4 h-4" />
                          <span>Estimated time: {step.estimatedTime}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Call to Action */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <button
            type="button"
            onClick={handleStartPlanning}
            className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            {HOW_IT_WORKS_CONTENT.ctaText}
          </button>
          <p className="text-sm text-neutral-500 mt-4">
            {HOW_IT_WORKS_CONTENT.trustMessage}
          </p>
        </motion.div>
      </div>
    </section>
  );
}
