(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1950:(t,e,i)=>{"use strict";i.d(e,{Tl:()=>s,Ux:()=>n,jx:()=>a});let a={name:"Blissful Trails",description:"Curated trips. Clean cabs. Cozy stays.",url:"https://blissfultrails.com",ogImage:"/og-image.jpg"},n={phone:"+91-9876543210",whatsapp:"+91-9876543210",email:"<EMAIL>"},s=[{label:"Packages",href:"#packages"},{label:"How It Works",href:"#how-it-works"},{label:"About",href:"#about"},{label:"Contact",href:"#contact"}]},2580:(t,e,i)=>{"use strict";i.d(e,{default:()=>x});var a=i(5155),n=i(2115),s=i(2605),l=i(760),o=i(9420),r=i(4416),c=i(4783),d=i(8883),h=i(1950),m=i(7335);function x(){let[t,e]=(0,n.useState)(!1),[i,x]=(0,n.useState)(!1),{scrollToElement:u}=(0,m.QV)();(0,n.useEffect)(()=>{let t=()=>{x(window.scrollY>50)};return window.addEventListener("scroll",t),()=>window.removeEventListener("scroll",t)},[]);let p=t=>{t.startsWith("#")?u(t.substring(1)):window.location.href=t,e(!1)};return(0,a.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(i?"bg-dark-900/95 backdrop-blur-md shadow-lg":"bg-dark-900/80 backdrop-blur-sm"),children:[(0,a.jsx)("div",{className:"container-custom",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,a.jsxs)(s.P.div,{className:"flex items-center space-x-2",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6},children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"B"})}),(0,a.jsx)("span",{className:"text-white font-heading font-bold text-xl lg:text-2xl",children:h.jx.name})]}),(0,a.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:h.Tl.map((t,e)=>(0,a.jsxs)(s.P.button,{onClick:()=>p(t.href),className:"text-white/90 hover:text-white font-medium transition-colors duration-200 relative group",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*e},children:[t.label,(0,a.jsx)("span",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full"})]},t.label))}),(0,a.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,a.jsxs)(s.P.a,{href:"tel:".concat(h.Ux.phone),className:"flex items-center space-x-2 text-white/90 hover:text-white transition-colors duration-200",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.4},children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:h.Ux.phone})]}),(0,a.jsx)(s.P.button,{onClick:()=>u("booking-form"),className:"btn-primary text-sm px-4 py-2",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.5},children:"Book Now"})]}),(0,a.jsx)(s.P.button,{onClick:()=>e(!t),className:"lg:hidden text-white p-2",initial:{opacity:0},animate:{opacity:1},transition:{duration:.6},children:t?(0,a.jsx)(r.A,{className:"w-6 h-6"}):(0,a.jsx)(c.A,{className:"w-6 h-6"})})]})}),(0,a.jsx)(l.N,{children:t&&(0,a.jsx)(s.P.div,{className:"lg:hidden bg-dark-900/98 backdrop-blur-md border-t border-white/10",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,a.jsxs)("div",{className:"container-custom py-6",children:[(0,a.jsx)("div",{className:"space-y-4 mb-6",children:h.Tl.map((t,e)=>(0,a.jsx)(s.P.button,{onClick:()=>p(t.href),className:"block w-full text-left text-white/90 hover:text-white font-medium py-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*e},children:t.label},t.label))}),(0,a.jsxs)("div",{className:"space-y-4 pt-4 border-t border-white/10",children:[(0,a.jsxs)(s.P.a,{href:"tel:".concat(h.Ux.phone),className:"flex items-center space-x-3 text-white/90 hover:text-white transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.4},children:[(0,a.jsx)(o.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"font-medium",children:h.Ux.phone})]}),(0,a.jsxs)(s.P.a,{href:"mailto:".concat(h.Ux.email),className:"flex items-center space-x-3 text-white/90 hover:text-white transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.5},children:[(0,a.jsx)(d.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"font-medium",children:h.Ux.email})]}),(0,a.jsx)(s.P.button,{onClick:()=>{u("booking-form"),e(!1)},className:"btn-primary w-full py-3 mt-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.6},children:"Book Your Trip Now"})]})]})})})]})}},5790:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,6707,23)),Promise.resolve().then(i.t.bind(i,9280,23)),Promise.resolve().then(i.t.bind(i,347,23)),Promise.resolve().then(i.bind(i,2580))},7335:(t,e,i)=>{"use strict";i.d(e,{DE:()=>n,QV:()=>s,u:()=>l});var a=i(2115);function n(){let[t,e]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{e(!0)},[]),t}function s(){let t=n()?document:null;return{scrollToElement:function(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"smooth";if(!t)return;let a=t.getElementById(e);a&&a.scrollIntoView({behavior:i})},scrollToTop:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"smooth";t&&window.scrollTo({top:0,behavior:e})}}}function l(){let t=n()?window:null;return{openLink:function(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"_blank";t&&t.open(e,i)},openWhatsApp:function(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(!t)return;let a=e.replace(/[^0-9]/g,""),n=encodeURIComponent(i),s="https://wa.me/".concat(a).concat(i?"?text=".concat(n):"");t.open(s,"_blank")}}}}},t=>{t.O(0,[781,96,441,358],()=>t(t.s=5790)),_N_E=t.O()}]);