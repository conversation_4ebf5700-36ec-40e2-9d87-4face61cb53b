{"name": "blissful-trails-homepage", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@tailwindcss/line-clamp": "^0.4.4", "axios": "^1.10.0", "clsx": "^2.1.1", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20.19.8", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "typescript": "^5"}}