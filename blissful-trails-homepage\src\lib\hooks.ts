'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to safely check if we're on the client side
 * Prevents hydration mismatches by ensuring client-only code runs after hydration
 */
export function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook for safe window operations
 * Returns null on server, actual window object on client after hydration
 */
export function useWindow() {
  const isClient = useIsClient();
  return isClient ? window : null;
}

/**
 * Hook for safe document operations
 * Returns null on server, actual document object on client after hydration
 */
export function useDocument() {
  const isClient = useIsClient();
  return isClient ? document : null;
}

/**
 * Hook for safe localStorage operations
 */
export function useLocalStorage() {
  const isClient = useIsClient();
  
  const getItem = (key: string): string | null => {
    if (!isClient) return null;
    try {
      return localStorage.getItem(key);
    } catch {
      return null;
    }
  };

  const setItem = (key: string, value: string): void => {
    if (!isClient) return;
    try {
      localStorage.setItem(key, value);
    } catch {
      // Silently fail
    }
  };

  const removeItem = (key: string): void => {
    if (!isClient) return;
    try {
      localStorage.removeItem(key);
    } catch {
      // Silently fail
    }
  };

  return { getItem, setItem, removeItem };
}

/**
 * Hook for safe scroll operations
 */
export function useScrollTo() {
  const document = useDocument();

  const scrollToElement = (elementId: string, behavior: ScrollBehavior = 'smooth') => {
    if (!document) return;
    
    const element = document.getElementById(elementId);
    if (element) {
      element.scrollIntoView({ behavior });
    }
  };

  const scrollToTop = (behavior: ScrollBehavior = 'smooth') => {
    if (!document) return;
    
    window.scrollTo({ top: 0, behavior });
  };

  return { scrollToElement, scrollToTop };
}

/**
 * Hook for safe external link operations
 */
export function useExternalLinks() {
  const windowObj = useWindow();

  const openLink = (url: string, target: string = '_blank') => {
    if (!windowObj) return;
    
    windowObj.open(url, target);
  };

  const openWhatsApp = (phoneNumber: string, message: string = '') => {
    if (!windowObj) return;
    
    const cleanPhone = phoneNumber.replace(/[^0-9]/g, '');
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${cleanPhone}${message ? `?text=${encodedMessage}` : ''}`;
    
    windowObj.open(whatsappUrl, '_blank');
  };

  return { openLink, openWhatsApp };
}
