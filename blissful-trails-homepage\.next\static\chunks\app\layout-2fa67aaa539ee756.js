(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},672:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>J});var i=a(5155),n=a(7930),l=a(7298),o=a(4516),r=a(3578),s=a(8491),c=a(5525),d=a(232),h=a(1976),m=a(9037),u=a(7580),x=a(1007),p=a(5670),b=a(646),w=a(2138),g=a(5169),f=a(7924),y=a(381),A=a(2355),v=a(3052),j=a(6474),N=a(7863),k=a(9420),C=a(1366),P=a(8883),U=a(224),T=a(4355),E=a(5502),M=a(3384),S=a(8136),_=a(5690),I=a(9074),z=a(4186),B=a(8564),L=a(333),D=a(1586),F=a(488),Q=a(5684),R=a(556),V=a(2925),W=a(4783),Y=a(4416),H=a(6932);let O={Car:n.A,Plane:l.A,MapPin:o.A,Map:r.A,Mountain:s.A,Shield:c.A,ShieldCheck:d.A,Heart:h.A,Award:m.A,Users:u.A,User:x.A,UserCheck:p.A,CheckCircle:b.A,ArrowRight:w.A,ArrowLeft:g.A,Search:f.A,Settings:y.A,ChevronLeft:A.A,ChevronRight:v.A,ChevronDown:j.A,ChevronUp:N.A,Phone:k.A,MessageCircle:C.A,Mail:P.A,Quote:U.A,Camera:T.A,Binoculars:E.A,Leaf:M.A,Building:S.A,Play:_.A,Calendar:I.A,Clock:z.A,Star:B.A,ThumbsUp:L.A,CreditCard:D.A,Facebook:F.A,Instagram:Q.A,Twitter:R.A,Youtube:V.A,Menu:W.A,X:Y.A,Filter:H.A},X={xs:"w-3 h-3",sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6",xl:"w-8 h-8","2xl":"w-10 h-10"},q={default:"text-neutral-600",primary:"text-primary-600",secondary:"text-secondary-600",success:"text-green-600",warning:"text-yellow-600",danger:"text-red-600"},G={default:"bg-neutral-100 text-neutral-600",primary:"bg-primary-100 text-primary-600",secondary:"bg-secondary-100 text-secondary-600",success:"bg-green-100 text-green-600",warning:"bg-yellow-100 text-yellow-600",danger:"bg-red-100 text-red-600"};function J(e){let{name:t,size:a="md",variant:n="default",background:l=!1,className:o="","aria-label":r,...s}=e,c=O[t];if(!c)return console.warn('Icon "'.concat(t,'" not found in iconMap')),null;let d=X[a],h=l?G[n]:q[n];return l?(0,i.jsx)("div",{className:"inline-flex items-center justify-center rounded-lg p-2 ".concat(h," ").concat(o),"aria-label":r,...s,children:(0,i.jsx)(c,{className:d})}):(0,i.jsx)(c,{className:"".concat(d," ").concat(h," ").concat(o),"aria-label":r,...s})}},1950:(e,t,a)=>{"use strict";a.d(t,{Tl:()=>l,Ux:()=>n,jx:()=>i});let i={name:"Blissful Trails",description:"Curated trips. Clean cabs. Cozy stays.",url:"https://blissfultrails.com",ogImage:"/og-image.jpg"},n={phone:"+91-9876543210",whatsapp:"+91-9876543210",email:"<EMAIL>"},l=[{label:"Packages",href:"#packages"},{label:"How It Works",href:"#how-it-works"},{label:"About",href:"#about"},{label:"Contact",href:"#contact"}]},2580:(e,t,a)=>{"use strict";a.d(t,{default:()=>d});var i=a(5155),n=a(2115),l=a(2605),o=a(760),r=a(1950),s=a(7335),c=a(672);function d(){let[e,t]=(0,n.useState)(!1),[a,d]=(0,n.useState)(!1),{scrollToElement:h}=(0,s.QV)();(0,n.useEffect)(()=>{let e=()=>{d(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let m=e=>{e.startsWith("#")?h(e.substring(1)):window.location.href=e,t(!1)};return(0,i.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(a?"bg-dark-900/95 backdrop-blur-md shadow-lg":"bg-dark-900/80 backdrop-blur-sm"),children:[(0,i.jsx)("div",{className:"container-custom",children:(0,i.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,i.jsxs)(l.P.div,{className:"flex items-center space-x-3",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6},children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,i.jsx)("span",{className:"text-white font-bold text-xl",children:"\uD83C\uDFD4️"})}),(0,i.jsx)("span",{className:"text-white font-heading font-bold text-xl lg:text-2xl",children:r.jx.name})]}),(0,i.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:r.Tl.map((e,t)=>(0,i.jsxs)(l.P.button,{onClick:()=>m(e.href),className:"text-white/90 hover:text-white font-medium transition-colors duration-200 relative group",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},children:[e.label,(0,i.jsx)("span",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full"})]},e.label))}),(0,i.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,i.jsxs)(l.P.a,{href:"tel:".concat(r.Ux.phone),className:"flex items-center space-x-2 text-white/90 hover:text-white transition-colors duration-200 bg-white/10 px-3 py-2 rounded-lg hover:bg-white/20",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.4},children:[(0,i.jsx)(c.Ay,{name:"Phone",size:"sm",className:"text-white/90"}),(0,i.jsx)("span",{className:"text-sm font-medium",children:r.Ux.phone})]}),(0,i.jsx)(l.P.button,{onClick:()=>h("booking-form"),className:"btn-primary text-sm px-4 py-2",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.5},children:"Book Now"})]}),(0,i.jsx)(l.P.button,{onClick:()=>t(!e),className:"lg:hidden text-white p-2 hover:bg-white/10 rounded-lg transition-colors duration-200",initial:{opacity:0},animate:{opacity:1},transition:{duration:.6},children:(0,i.jsx)(c.Ay,{name:e?"X":"Menu",size:"lg",className:"text-white"})})]})}),(0,i.jsx)(o.N,{children:e&&(0,i.jsx)(l.P.div,{className:"lg:hidden bg-dark-900/98 backdrop-blur-md border-t border-white/10",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,i.jsxs)("div",{className:"container-custom py-6",children:[(0,i.jsx)("div",{className:"space-y-4 mb-6",children:r.Tl.map((e,t)=>(0,i.jsx)(l.P.button,{onClick:()=>m(e.href),className:"block w-full text-left text-white/90 hover:text-white font-medium py-2 transition-colors duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*t},children:e.label},e.label))}),(0,i.jsxs)("div",{className:"space-y-4 pt-4 border-t border-white/10",children:[(0,i.jsxs)(l.P.a,{href:"tel:".concat(r.Ux.phone),className:"flex items-center space-x-3 text-white/90 hover:text-white transition-colors duration-200 bg-white/5 p-3 rounded-lg hover:bg-white/10",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.4},children:[(0,i.jsx)(c.Ay,{name:"Phone",size:"md",className:"text-white/90"}),(0,i.jsx)("span",{className:"font-medium",children:r.Ux.phone})]}),(0,i.jsxs)(l.P.a,{href:"mailto:".concat(r.Ux.email),className:"flex items-center space-x-3 text-white/90 hover:text-white transition-colors duration-200 bg-white/5 p-3 rounded-lg hover:bg-white/10",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.5},children:[(0,i.jsx)(c.Ay,{name:"Mail",size:"md",className:"text-white/90"}),(0,i.jsx)("span",{className:"font-medium",children:r.Ux.email})]}),(0,i.jsx)(l.P.button,{onClick:()=>{h("booking-form"),t(!1)},className:"btn-primary w-full py-3 mt-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.6},children:"Book Your Trip Now"})]})]})})})]})}},5790:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,6707,23)),Promise.resolve().then(a.t.bind(a,9280,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,2580))},7335:(e,t,a)=>{"use strict";a.d(t,{DE:()=>n,QV:()=>l,u:()=>o});var i=a(2115);function n(){let[e,t]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{t(!0)},[]),e}function l(){let e=n()?document:null;return{scrollToElement:function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"smooth";if(!e)return;let i=e.getElementById(t);i&&i.scrollIntoView({behavior:a})},scrollToTop:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"smooth";e&&window.scrollTo({top:0,behavior:t})}}}function o(){let e=n()?window:null;return{openLink:function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"_blank";e&&e.open(t,a)},openWhatsApp:function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(!e)return;let i=t.replace(/[^0-9]/g,""),n=encodeURIComponent(a),l="https://wa.me/".concat(i).concat(a?"?text=".concat(n):"");e.open(l,"_blank")}}}}},e=>{e.O(0,[781,96,441,358],()=>e(e.s=5790)),_N_E=e.O()}]);