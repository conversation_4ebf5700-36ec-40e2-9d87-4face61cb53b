import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/common/Navigation";

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-poppins',
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: 'Blissful Trails - Family Travel Packages | North Bengal & Sikkim Tours',
    template: '%s | Blissful Trails'
  },
  description: 'Experience hassle-free family travel with hygiene-verified accommodations, clean cabs, and 24/7 support. Customized tour packages for Darjeeling, Sikkim, and Dooars.',
  keywords: [
    'family travel packages',
    'Darjeeling tour packages',
    'Sikkim family tours',
    'North Bengal travel',
    'hygiene verified hotels',
    'family-friendly travel',
    'Dooars wildlife tours',
    'hill station packages',
    'clean cab services',
    'customized itinerary'
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <body className={`${inter.className} antialiased`}>
        <Navigation />
        {children}
      </body>
    </html>
  );
}
