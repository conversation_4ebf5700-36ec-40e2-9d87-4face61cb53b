interface PlaceholderImageProps {
  title: string;
  width: number;
  height: number;
  className?: string;
}

export default function PlaceholderImage({ title, width, height, className = '' }: PlaceholderImageProps) {
  // Generate realistic travel imagery based on title content
  const getImageTheme = (str: string) => {
    const titleLower = str.toLowerCase();

    // Specific themes based on actual content
    if (titleLower.includes('darjeeling') || titleLower.includes('tea')) {
      return {
        gradient: 'linear-gradient(135deg, #2d5016 0%, #4a7c59 50%, #87ceeb 100%)',
        icon: '🫖',
        description: 'Tea Gardens & Hills',
        pattern: 'tea-gardens'
      };
    }

    if (titleLower.includes('sikkim') || titleLower.includes('gangtok')) {
      return {
        gradient: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #ffffff 100%)',
        icon: '🏔️',
        description: 'Himalayan Peaks',
        pattern: 'mountains'
      };
    }

    if (titleLower.includes('dooars') || titleLower.includes('wildlife') || titleLower.includes('jungle')) {
      return {
        gradient: 'linear-gradient(135deg, #0f4c3a 0%, #2d5a27 50%, #8fbc8f 100%)',
        icon: '🐘',
        description: 'Wildlife & Forests',
        pattern: 'jungle'
      };
    }

    if (titleLower.includes('mirik') || titleLower.includes('lake')) {
      return {
        gradient: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #87ceeb 100%)',
        icon: '🏞️',
        description: 'Serene Lakes',
        pattern: 'lake'
      };
    }

    if (titleLower.includes('kalimpong') || titleLower.includes('heritage')) {
      return {
        gradient: 'linear-gradient(135deg, #8b4513 0%, #cd853f 50%, #f4a460 100%)',
        icon: '🏛️',
        description: 'Heritage & Culture',
        pattern: 'heritage'
      };
    }

    if (titleLower.includes('family') || titleLower.includes('couple')) {
      return {
        gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ffa500 50%, #ffd700 100%)',
        icon: '👨‍👩‍👧‍👦',
        description: 'Family Adventures',
        pattern: 'family'
      };
    }

    // Default travel theme
    return {
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      icon: '🌄',
      description: 'Scenic Beauty',
      pattern: 'scenic'
    };
  };

  const theme = getImageTheme(title);

  // Create pattern-specific overlays
  const getPatternOverlay = (pattern: string) => {
    const patterns = {
      'tea-gardens': `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='15' r='3'/%3E%3Ccircle cx='15' cy='30' r='2'/%3E%3Ccircle cx='45' cy='30' r='2'/%3E%3Ccircle cx='30' cy='45' r='3'/%3E%3C/g%3E%3C/svg%3E")`,
      'mountains': `url("data:image/svg+xml,%3Csvg width='80' height='40' viewBox='0 0 80 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.15'%3E%3Cpath d='M0 40L20 10L40 25L60 5L80 30L80 40Z'/%3E%3C/g%3E%3C/svg%3E")`,
      'jungle': `url("data:image/svg+xml,%3Csvg width='50' height='50' viewBox='0 0 50 50' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M25 5C30 10 35 15 25 25C15 15 20 10 25 5Z'/%3E%3Cpath d='M10 35C15 30 20 35 15 45C10 35 10 35 10 35Z'/%3E%3C/g%3E%3C/svg%3E")`,
      'lake': `url("data:image/svg+xml,%3Csvg width='60' height='30' viewBox='0 0 60 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M0 15C15 10 30 20 45 15C50 15 55 15 60 15L60 30L0 30Z'/%3E%3C/g%3E%3C/svg%3E")`,
      'heritage': `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Crect x='5' y='10' width='30' height='25'/%3E%3Cpath d='M20 5L10 10L30 10Z'/%3E%3C/g%3E%3C/svg%3E")`,
      'family': `url("data:image/svg+xml,%3Csvg width='50' height='50' viewBox='0 0 50 50' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='15' cy='15' r='5'/%3E%3Ccircle cx='35' cy='15' r='5'/%3E%3Ccircle cx='25' cy='35' r='8'/%3E%3C/g%3E%3C/svg%3E")`,
      'scenic': `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 10L40 30L20 30Z'/%3E%3Ccircle cx='45' cy='15' r='8'/%3E%3C/g%3E%3C/svg%3E")`
    };
    return patterns[pattern as keyof typeof patterns] || patterns.scenic;
  };

  return (
    <div
      className={`flex flex-col items-center justify-center text-white font-semibold text-center p-6 relative overflow-hidden ${className}`}
      style={{
        background: theme.gradient,
        width: `${width}px`,
        height: `${height}px`,
      }}
    >
      {/* Pattern overlay specific to content type */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: getPatternOverlay(theme.pattern),
          backgroundSize: '60px 60px',
          backgroundRepeat: 'repeat'
        }}
      />

      {/* Content overlay */}
      <div className="absolute inset-0 bg-black/20" />

      <div className="relative z-10 max-w-full">
        <div className="text-5xl mb-4 drop-shadow-lg">
          {theme.icon}
        </div>
        <div className="text-lg md:text-xl font-heading leading-tight mb-3 drop-shadow-md">
          {title}
        </div>
        <div className="text-sm opacity-90 mb-2 font-medium">
          {theme.description}
        </div>
        <div className="text-xs opacity-75 bg-white/10 px-3 py-1 rounded-full backdrop-blur-sm">
          Premium imagery coming soon
        </div>
      </div>
    </div>
  );
}
