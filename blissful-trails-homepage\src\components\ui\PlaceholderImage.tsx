interface PlaceholderImageProps {
  title: string;
  width: number;
  height: number;
  className?: string;
}

export default function PlaceholderImage({ title, width, height, className = '' }: PlaceholderImageProps) {
  // Generate a consistent color based on the title
  const getColorFromTitle = (str: string) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    // Generate pleasant colors for travel themes
    const colors = [
      'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // Purple-blue
      'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', // Pink-red
      'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', // Blue-cyan
      'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', // Green-cyan
      'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', // Pink-yellow
      'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', // Cyan-pink
      'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', // Pink-purple
      'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)', // Orange-peach
    ];
    
    return colors[Math.abs(hash) % colors.length];
  };

  const backgroundStyle = {
    background: getColorFromTitle(title),
    width: `${width}px`,
    height: `${height}px`,
  };

  return (
    <div 
      className={`flex items-center justify-center text-white font-semibold text-center p-4 ${className}`}
      style={backgroundStyle}
    >
      <div className="max-w-full">
        <div className="text-lg md:text-xl font-heading leading-tight">
          {title}
        </div>
        <div className="text-sm opacity-80 mt-2">
          Coming Soon
        </div>
      </div>
    </div>
  );
}
