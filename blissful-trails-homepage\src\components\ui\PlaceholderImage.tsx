interface PlaceholderImageProps {
  title: string;
  width: number;
  height: number;
  className?: string;
}

export default function PlaceholderImage({ title, width, height, className = '' }: PlaceholderImageProps) {
  // Generate a consistent color and theme based on the title
  const getImageTheme = (str: string) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Travel-themed gradients and icons
    const themes = [
      {
        gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        icon: '🏔️',
        description: 'Mountain Views'
      },
      {
        gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        icon: '🌊',
        description: 'Scenic Waters'
      },
      {
        gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        icon: '🌲',
        description: 'Forest Trails'
      },
      {
        gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        icon: '🌅',
        description: 'Sunrise Views'
      },
      {
        gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        icon: '🏛️',
        description: 'Heritage Sites'
      },
      {
        gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
        icon: '🦋',
        description: 'Wildlife Safari'
      },
      {
        gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        icon: '🚡',
        description: 'Cable Car Rides'
      },
      {
        gradient: 'linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%)',
        icon: '🏞️',
        description: 'Valley Views'
      }
    ];

    return themes[Math.abs(hash) % themes.length];
  };

  const theme = getImageTheme(title);

  const backgroundStyle = {
    background: theme.gradient,
    width: `${width}px`,
    height: `${height}px`,
  };

  return (
    <div
      className={`flex flex-col items-center justify-center text-white font-semibold text-center p-6 relative overflow-hidden ${className}`}
      style={backgroundStyle}
    >
      {/* Decorative pattern overlay */}
      <div className="absolute inset-0 opacity-10" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20v-40c11.046 0 20 8.954 20 20z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundSize: '40px 40px'
      }} />

      <div className="relative z-10 max-w-full">
        <div className="text-4xl mb-3">
          {theme.icon}
        </div>
        <div className="text-lg md:text-xl font-heading leading-tight mb-2">
          {title}
        </div>
        <div className="text-sm opacity-90 mb-1">
          {theme.description}
        </div>
        <div className="text-xs opacity-70">
          High-quality image coming soon
        </div>
      </div>
    </div>
  );
}
