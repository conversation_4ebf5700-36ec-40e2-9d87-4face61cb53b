'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Facebook, 
  Instagram, 
  Twitter, 
  Youtube, 
  Mail, 
  Phone, 
  MapPin, 
  Award, 
  Shield, 
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { FOOTER_CONTENT } from '@/data/content';
import { CONTACT_INFO } from '@/lib/constants';

const iconMap = {
  Facebook,
  Instagram,
  Twitter,
  Youtube,
  Award,
  Shield,
  CheckCircle
};

export default function Footer() {
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsSubscribing(true);
    
    // Simulate API call
    setTimeout(() => {
      setSubscriptionStatus('success');
      setIsSubscribing(false);
      setEmail('');
      
      // Reset status after 3 seconds
      setTimeout(() => setSubscriptionStatus('idle'), 3000);
    }, 1000);
  };

  return (
    <footer className="bg-neutral-900 text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h3 className="text-2xl font-heading font-bold mb-4">
                {FOOTER_CONTENT.companyInfo.name}
              </h3>
              <p className="text-lg text-primary-400 mb-4">
                {FOOTER_CONTENT.companyInfo.tagline}
              </p>
              <p className="text-neutral-300 mb-6 leading-relaxed">
                {FOOTER_CONTENT.companyInfo.description}
              </p>

              {/* Contact Info */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-3">
                  <Phone className="w-5 h-5 text-primary-400" />
                  <span className="text-neutral-300">{CONTACT_INFO.phone}</span>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-primary-400" />
                  <span className="text-neutral-300">{CONTACT_INFO.email}</span>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="w-5 h-5 text-primary-400" />
                  <span className="text-neutral-300">North Bengal & Sikkim, India</span>
                </div>
              </div>

              {/* Social Media */}
              <div className="flex gap-4">
                {FOOTER_CONTENT.socialMedia.map((social) => {
                  const IconComponent = iconMap[social.icon as keyof typeof iconMap];
                  return (
                    <a
                      key={social.platform}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 bg-neutral-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-300"
                      aria-label={`Follow us on ${social.platform}`}
                    >
                      <IconComponent className="w-5 h-5" />
                    </a>
                  );
                })}
              </div>
            </motion.div>
          </div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {FOOTER_CONTENT.quickLinks.map((link) => (
                <li key={link.label}>
                  <a
                    href={link.href}
                    className="text-neutral-300 hover:text-primary-400 transition-colors duration-300"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Destinations */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h4 className="text-lg font-semibold mb-6">Destinations</h4>
            <ul className="space-y-3">
              {FOOTER_CONTENT.destinations.map((destination) => (
                <li key={destination.label}>
                  <a
                    href={destination.href}
                    className="text-neutral-300 hover:text-primary-400 transition-colors duration-300"
                  >
                    {destination.label}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Support & Newsletter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h4 className="text-lg font-semibold mb-6">Support</h4>
            <ul className="space-y-3 mb-8">
              {FOOTER_CONTENT.support.slice(0, 4).map((support) => (
                <li key={support.label}>
                  <a
                    href={support.href}
                    className="text-neutral-300 hover:text-primary-400 transition-colors duration-300"
                  >
                    {support.label}
                  </a>
                </li>
              ))}
            </ul>

            {/* Newsletter Signup */}
            <div className="bg-neutral-800 rounded-lg p-4">
              <h5 className="font-semibold mb-2">{FOOTER_CONTENT.newsletter.headline}</h5>
              <p className="text-sm text-neutral-400 mb-4">
                {FOOTER_CONTENT.newsletter.description}
              </p>
              
              {subscriptionStatus === 'success' ? (
                <div className="text-green-400 text-sm font-medium">
                  ✓ Successfully subscribed!
                </div>
              ) : (
                <form onSubmit={handleNewsletterSubmit} className="space-y-3">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder={FOOTER_CONTENT.newsletter.placeholder}
                    className="w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-lg text-white placeholder-neutral-400 focus:outline-none focus:border-primary-500"
                    required
                  />
                  <button
                    type="submit"
                    disabled={isSubscribing}
                    className="w-full bg-primary-600 hover:bg-primary-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-300 flex items-center justify-center gap-2"
                  >
                    {isSubscribing ? 'Subscribing...' : FOOTER_CONTENT.newsletter.buttonText}
                    {!isSubscribing && <ArrowRight className="w-4 h-4" />}
                  </button>
                </form>
              )}
              
              <p className="text-xs text-neutral-500 mt-2">
                {FOOTER_CONTENT.newsletter.privacyText}
              </p>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-neutral-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            {/* Copyright */}
            <div className="text-neutral-400 text-sm">
              {FOOTER_CONTENT.copyright}
            </div>

            {/* Certifications */}
            <div className="flex items-center gap-6">
              {FOOTER_CONTENT.certifications.map((cert) => {
                const IconComponent = iconMap[cert.icon as keyof typeof iconMap];
                return (
                  <div key={cert.name} className="flex items-center gap-2">
                    <IconComponent className="w-4 h-4 text-primary-400" />
                    <span className="text-sm text-neutral-400">{cert.name}</span>
                  </div>
                );
              })}
            </div>

            {/* Legal Links */}
            <div className="flex gap-4">
              {FOOTER_CONTENT.legal.map((legal, index) => (
                <span key={legal.label} className="flex items-center gap-4">
                  <a
                    href={legal.href}
                    className="text-sm text-neutral-400 hover:text-primary-400 transition-colors duration-300"
                  >
                    {legal.label}
                  </a>
                  {index < FOOTER_CONTENT.legal.length - 1 && (
                    <span className="text-neutral-600">|</span>
                  )}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
