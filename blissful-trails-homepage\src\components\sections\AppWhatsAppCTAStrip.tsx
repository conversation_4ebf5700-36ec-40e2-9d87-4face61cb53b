'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, Smartphone, X, Download } from 'lucide-react';
import { APP_WHATSAPP_CTA_CONTENT } from '@/data/content';
import { CONTACT_INFO } from '@/lib/constants';
import { useExternalLinks, useIsClient } from '@/lib/hooks';

// Icon components for potential future use
// const iconMap = {
//   MessageCircle,
//   Smartphone,
//   Download,
//   Star,
//   Users,
//   Shield
// };

export default function AppWhatsAppCTAStrip() {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  const isClient = useIsClient();
  const { openWhatsApp, openLink } = useExternalLinks();

  useEffect(() => {
    if (!isClient) return;

    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 3000); // Show after 3 seconds

    return () => clearTimeout(timer);
  }, [isClient]);

  const handleWhatsAppClick = () => {
    openWhatsApp(CONTACT_INFO.whatsapp, APP_WHATSAPP_CTA_CONTENT.whatsappCTA.message);
  };

  const handleAppStoreClick = (url: string) => {
    openLink(url);
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    setIsVisible(false);
  };

  if (isDismissed || !isClient) return null;

  return (
    <>
      {/* Main CTA Strip Section */}
      <section className="py-16 bg-gradient-to-r from-primary-600 to-primary-700 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <motion.h2
              className="text-3xl md:text-4xl font-heading font-bold mb-4"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              {APP_WHATSAPP_CTA_CONTENT.headline}
            </motion.h2>
            <motion.p
              className="text-xl mb-2 opacity-90"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              {APP_WHATSAPP_CTA_CONTENT.subheadline}
            </motion.p>
            <motion.p
              className="text-lg opacity-80 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {APP_WHATSAPP_CTA_CONTENT.description}
            </motion.p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* WhatsApp CTA */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Instant WhatsApp Support</h3>
              <p className="text-white/80 mb-4">Get immediate assistance and personalized recommendations</p>
              <button
                type="button"
                onClick={handleWhatsAppClick}
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 w-full"
              >
                {APP_WHATSAPP_CTA_CONTENT.whatsappCTA.text}
              </button>
              <div className="mt-3 text-sm text-white/70">
                {APP_WHATSAPP_CTA_CONTENT.offers[1].text}
              </div>
            </motion.div>

            {/* App Download CTA */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="w-16 h-16 bg-accent-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Smartphone className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{APP_WHATSAPP_CTA_CONTENT.appDownload.headline}</h3>
              <p className="text-white/80 mb-4">{APP_WHATSAPP_CTA_CONTENT.appDownload.description}</p>
              
              <div className="space-y-3">
                <button
                  type="button"
                  onClick={() => handleAppStoreClick(APP_WHATSAPP_CTA_CONTENT.appDownload.playStore.url)}
                  className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 w-full flex items-center justify-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  {APP_WHATSAPP_CTA_CONTENT.appDownload.playStore.text}
                </button>
                <button
                  type="button"
                  onClick={() => handleAppStoreClick(APP_WHATSAPP_CTA_CONTENT.appDownload.appStore.url)}
                  className="bg-black hover:bg-gray-800 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 w-full flex items-center justify-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  {APP_WHATSAPP_CTA_CONTENT.appDownload.appStore.text}
                </button>
              </div>
              
              <div className="mt-3 text-sm text-accent-200 font-medium">
                {APP_WHATSAPP_CTA_CONTENT.offers[0].text}
              </div>
            </motion.div>
          </div>

          {/* Trust Badges */}
          <motion.div
            className="flex flex-wrap justify-center gap-6 mt-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            {APP_WHATSAPP_CTA_CONTENT.trustBadges.map((badge, index) => (
              <div key={index} className="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                <div className="w-2 h-2 bg-secondary-400 rounded-full" />
                <span className="text-sm font-medium">{badge}</span>
              </div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Sticky Notification Bar */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            className="fixed bottom-0 left-0 right-0 z-50 bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg"
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="container mx-auto px-4 py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <MessageCircle className="w-5 h-5" />
                  <div>
                    <p className="font-medium text-sm">Need help planning your trip?</p>
                    <p className="text-xs opacity-90">Chat with us on WhatsApp for instant assistance</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    type="button"
                    onClick={handleWhatsAppClick}
                    className="bg-white text-green-600 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-100 transition-colors"
                  >
                    Chat Now
                  </button>
                  <button
                    type="button"
                    onClick={handleDismiss}
                    className="p-1 hover:bg-white/20 rounded transition-colors"
                    title="Dismiss notification"
                    aria-label="Dismiss notification"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
