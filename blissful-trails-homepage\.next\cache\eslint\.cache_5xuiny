[{"C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\layout.tsx": "1", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\page.tsx": "2", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\AppWhatsAppCTAStrip.tsx": "3", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\BookingForm.tsx": "4", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\Footer.tsx": "5", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HeroSection.tsx": "6", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HowItWorksSection.tsx": "7", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\PackagesCarousel.tsx": "8", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\TestimonialsSection.tsx": "9", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\TrustSafetySection.tsx": "10", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\ValuePropositionSection.tsx": "11", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\ui\\PlaceholderImage.tsx": "12", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\data\\content.ts": "13", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\constants.ts": "14", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\hooks.ts": "15", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\utils.ts": "16", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\types\\index.ts": "17", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\common\\Navigation.tsx": "18"}, {"size": 1395, "mtime": 1753027034952, "results": "19", "hashOfConfig": "20"}, {"size": 945, "mtime": 1752834623320, "results": "21", "hashOfConfig": "20"}, {"size": 8884, "mtime": 1752841873435, "results": "22", "hashOfConfig": "20"}, {"size": 33330, "mtime": 1752841972095, "results": "23", "hashOfConfig": "20"}, {"size": 9627, "mtime": 1752834317954, "results": "24", "hashOfConfig": "20"}, {"size": 5490, "mtime": 1753027460793, "results": "25", "hashOfConfig": "20"}, {"size": 14386, "mtime": 1753027532363, "results": "26", "hashOfConfig": "20"}, {"size": 16765, "mtime": 1753027192453, "results": "27", "hashOfConfig": "20"}, {"size": 12695, "mtime": 1753027584478, "results": "28", "hashOfConfig": "20"}, {"size": 7221, "mtime": 1753027557274, "results": "29", "hashOfConfig": "20"}, {"size": 2446, "mtime": 1753027093725, "results": "30", "hashOfConfig": "20"}, {"size": 3009, "mtime": 1753027444029, "results": "31", "hashOfConfig": "20"}, {"size": 28329, "mtime": 1752841182866, "results": "32", "hashOfConfig": "20"}, {"size": 550, "mtime": 1752769294386, "results": "33", "hashOfConfig": "20"}, {"size": 2731, "mtime": 1752841203700, "results": "34", "hashOfConfig": "20"}, {"size": 166, "mtime": 1752769335996, "results": "35", "hashOfConfig": "20"}, {"size": 552, "mtime": 1752769493152, "results": "36", "hashOfConfig": "20"}, {"size": 7069, "mtime": 1753027015656, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kfznf5", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\layout.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\page.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\AppWhatsAppCTAStrip.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\BookingForm.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\Footer.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HeroSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HowItWorksSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\PackagesCarousel.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\TestimonialsSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\TrustSafetySection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\ValuePropositionSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\ui\\PlaceholderImage.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\data\\content.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\constants.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\hooks.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\utils.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\types\\index.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\common\\Navigation.tsx", [], []]