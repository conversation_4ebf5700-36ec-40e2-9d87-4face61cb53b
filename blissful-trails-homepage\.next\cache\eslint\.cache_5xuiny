[{"C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\layout.tsx": "1", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\page.tsx": "2", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\AppWhatsAppCTAStrip.tsx": "3", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\BookingForm.tsx": "4", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\Footer.tsx": "5", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HeroSection.tsx": "6", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HowItWorksSection.tsx": "7", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\PackagesCarousel.tsx": "8", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\TestimonialsSection.tsx": "9", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\TrustSafetySection.tsx": "10", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\ValuePropositionSection.tsx": "11", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\ui\\PlaceholderImage.tsx": "12", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\data\\content.ts": "13", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\constants.ts": "14", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\hooks.ts": "15", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\utils.ts": "16", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\types\\index.ts": "17", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\common\\Navigation.tsx": "18", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\ui\\EnhancedIcon.tsx": "19"}, {"size": 1395, "mtime": 1753027034952, "results": "20", "hashOfConfig": "21"}, {"size": 945, "mtime": 1752834623320, "results": "22", "hashOfConfig": "21"}, {"size": 8884, "mtime": 1752841873435, "results": "23", "hashOfConfig": "21"}, {"size": 33418, "mtime": 1753028678848, "results": "24", "hashOfConfig": "21"}, {"size": 9627, "mtime": 1752834317954, "results": "25", "hashOfConfig": "21"}, {"size": 5490, "mtime": 1753027460793, "results": "26", "hashOfConfig": "21"}, {"size": 14386, "mtime": 1753027532363, "results": "27", "hashOfConfig": "21"}, {"size": 16768, "mtime": 1753028702141, "results": "28", "hashOfConfig": "21"}, {"size": 12695, "mtime": 1753027584478, "results": "29", "hashOfConfig": "21"}, {"size": 7221, "mtime": 1753027557274, "results": "30", "hashOfConfig": "21"}, {"size": 2480, "mtime": 1753028549998, "results": "31", "hashOfConfig": "21"}, {"size": 5818, "mtime": 1753028470882, "results": "32", "hashOfConfig": "21"}, {"size": 28329, "mtime": 1752841182866, "results": "33", "hashOfConfig": "21"}, {"size": 550, "mtime": 1752769294386, "results": "34", "hashOfConfig": "21"}, {"size": 2731, "mtime": 1752841203700, "results": "35", "hashOfConfig": "21"}, {"size": 166, "mtime": 1752769335996, "results": "36", "hashOfConfig": "21"}, {"size": 552, "mtime": 1752769493152, "results": "37", "hashOfConfig": "21"}, {"size": 7482, "mtime": 1753028617371, "results": "38", "hashOfConfig": "21"}, {"size": 4661, "mtime": 1753028520300, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kfznf5", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\layout.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\page.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\AppWhatsAppCTAStrip.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\BookingForm.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\Footer.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HeroSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HowItWorksSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\PackagesCarousel.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\TestimonialsSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\TrustSafetySection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\ValuePropositionSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\ui\\PlaceholderImage.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\data\\content.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\constants.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\hooks.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\utils.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\types\\index.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\common\\Navigation.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\ui\\EnhancedIcon.tsx", [], []]