export interface ValueProposition {
  id: string;
  icon: string;
  title: string;
  description: string;
}

export interface HeroContent {
  headline: string;
  subheadline: string;
  description: string;
  primaryCTA: string;
  secondaryCTA: string;
  trustBadges: readonly string[];
}

export interface NavigationLink {
  label: string;
  href: string;
}

export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  ogImage: string;
}

export interface ContactInfo {
  phone: string;
  whatsapp: string;
  email: string;
}
