'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { 
  Star, 
  Users, 
  ThumbsUp, 
  Award, 
  ChevronLeft, 
  ChevronRight, 
  Quote,
  MapPin,
  Calendar,
  CheckCircle
} from 'lucide-react';
import { 
  TESTIMONIALS_CONTENT, 
  TESTIMONIALS_DATA, 
  TESTIMONIAL_CATEGORIES, 
  TESTIMONIAL_STATS 
} from '@/data/content';

const iconMap = {
  Star,
  Users,
  ThumbsUp,
  Award
};

export default function TestimonialsSection() {
  const [activeCategory, setActiveCategory] = useState('All Stories');
  const [currentIndex, setCurrentIndex] = useState(0);

  // Filter testimonials based on active category
  const filteredTestimonials = activeCategory === 'All Stories' 
    ? TESTIMONIALS_DATA 
    : TESTIMONIALS_DATA.filter(testimonial => testimonial.category === activeCategory);

  // Get featured testimonials for the main carousel
  const featuredTestimonials = filteredTestimonials.filter(testimonial => testimonial.featured);
  const displayTestimonials = featuredTestimonials.length > 0 ? featuredTestimonials : filteredTestimonials.slice(0, 3);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + displayTestimonials.length) % displayTestimonials.length);
  };

  const currentTestimonial = displayTestimonials[currentIndex];

  return (
    <section className="py-20 bg-gradient-to-br from-neutral-50 to-primary-50/30">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 mb-4">
            {TESTIMONIALS_CONTENT.headline}
          </h2>
          <p className="text-xl text-primary-600 mb-6 font-medium">
            {TESTIMONIALS_CONTENT.subheadline}
          </p>
          <p className="text-lg text-neutral-600 max-w-3xl mx-auto">
            {TESTIMONIALS_CONTENT.description}
          </p>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {TESTIMONIAL_STATS.map((stat, index) => {
            const IconComponent = iconMap[stat.icon as keyof typeof iconMap];

            return (
              <motion.div
                key={index}
                className="text-center bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <IconComponent className="w-8 h-8 text-primary-600 mx-auto mb-3" />
                <div className="text-2xl md:text-3xl font-bold text-neutral-900 mb-1">
                  {stat.number}
                </div>
                <div className="text-sm text-neutral-600">
                  {stat.label}
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-3 mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          {TESTIMONIAL_CATEGORIES.map((category) => (
            <button
              type="button"
              key={category}
              onClick={() => {
                setActiveCategory(category);
                setCurrentIndex(0);
              }}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category
                  ? 'bg-primary-600 text-white shadow-lg'
                  : 'bg-white text-neutral-600 hover:bg-primary-50 hover:text-primary-600'
              }`}
            >
              {category}
            </button>
          ))}
        </motion.div>

        {/* Main Testimonial Carousel */}
        {currentTestimonial && (
          <motion.div
            className="max-w-4xl mx-auto mb-16"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
              <div className="md:flex">
                {/* Image Section */}
                <div className="md:w-1/3 relative h-64 md:h-auto">
                  <Image
                    src={currentTestimonial.image}
                    alt={`${currentTestimonial.name} - Happy travelers`}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 33vw"
                  />
                  <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full flex items-center gap-1">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-medium text-neutral-700">Verified</span>
                  </div>
                </div>

                {/* Content Section */}
                <div className="md:w-2/3 p-8">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-neutral-900 mb-1">
                        {currentTestimonial.name}
                      </h3>
                      <div className="flex items-center gap-4 text-sm text-neutral-600 mb-2">
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          {currentTestimonial.location}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {currentTestimonial.date}
                        </div>
                      </div>
                      <div className="text-sm text-primary-600 font-medium mb-3">
                        {currentTestimonial.tripDestination} • {currentTestimonial.packageBooked}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-5 h-5 ${
                            i < currentTestimonial.rating
                              ? 'text-yellow-500 fill-current'
                              : 'text-neutral-300'
                          }`}
                        />
                      ))}
                    </div>
                  </div>

                  <Quote className="w-8 h-8 text-primary-200 mb-4" />
                  <p className="text-neutral-700 leading-relaxed mb-6 italic">
                    &ldquo;{currentTestimonial.quote}&rdquo;
                  </p>

                  {/* Highlights */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {currentTestimonial.highlights.map((highlight, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm text-neutral-600">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full flex-shrink-0" />
                        {highlight}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-between mt-6">
              <button
                type="button"
                onClick={prevTestimonial}
                className="flex items-center gap-2 px-4 py-2 text-neutral-600 hover:text-primary-600 transition-colors duration-300"
                disabled={displayTestimonials.length <= 1}
              >
                <ChevronLeft className="w-5 h-5" />
                Previous
              </button>

              <div className="flex gap-2">
                {displayTestimonials.map((_, index) => (
                  <button
                    type="button"
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentIndex
                        ? 'bg-primary-600'
                        : 'bg-neutral-300 hover:bg-primary-300'
                    }`}
                    aria-label={`Go to testimonial ${index + 1}`}
                    title={`Go to testimonial ${index + 1}`}
                  />
                ))}
              </div>

              <button
                type="button"
                onClick={nextTestimonial}
                className="flex items-center gap-2 px-4 py-2 text-neutral-600 hover:text-primary-600 transition-colors duration-300"
                disabled={displayTestimonials.length <= 1}
              >
                Next
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          </motion.div>
        )}

        {/* All Testimonials Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {filteredTestimonials.slice(0, 6).map((testimonial) => (
            <motion.div
              key={testimonial.id}
              className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
              whileHover={{ scale: 1.02 }}
              onClick={() => setCurrentIndex(displayTestimonials.findIndex(t => t.id === testimonial.id))}
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="relative w-12 h-12 rounded-full overflow-hidden">
                  <Image
                    src={testimonial.image}
                    alt={testimonial.name}
                    fill
                    className="object-cover"
                    sizes="48px"
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-neutral-900 text-sm">
                    {testimonial.name}
                  </h4>
                  <div className="flex items-center gap-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-3 h-3 ${
                          i < testimonial.rating
                            ? 'text-yellow-500 fill-current'
                            : 'text-neutral-300'
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-neutral-600 text-sm line-clamp-3">
                &ldquo;{testimonial.quote}&rdquo;
              </p>
              <div className="mt-3 text-xs text-primary-600 font-medium">
                {testimonial.tripDestination}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <p className="text-lg text-neutral-600 mb-8 max-w-2xl mx-auto">
            &ldquo;{TESTIMONIALS_CONTENT.trustMessage}&rdquo;
          </p>
          <motion.button
            className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {TESTIMONIALS_CONTENT.ctaText}
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
