'use client';

import { motion } from 'framer-motion';
import { VALUE_PROPOSITIONS } from '@/data/content';
import EnhancedIcon, { IconName } from '@/components/ui/EnhancedIcon';

export default function ValuePropositionSection() {
  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-section-mobile md:text-section font-heading font-bold text-neutral-900 mb-6">
            Why Families Choose Blissful Trails
          </h2>
          <p className="text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
            We understand Indian family travel needs and deliver experiences that prioritize safety, comfort, and convenience.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          {VALUE_PROPOSITIONS.map((item, index) => {
            return (
              <motion.div
                key={item.id}
                className="card p-8 text-center group hover:scale-105 transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                data-testid="value-prop-card"
              >
                <div className="w-20 h-20 bg-primary-100 rounded-3xl flex items-center justify-center mb-6 mx-auto group-hover:bg-primary-500 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                  <EnhancedIcon
                    name={item.icon as IconName}
                    size="2xl"
                    variant="primary"
                    className="group-hover:text-white transition-colors duration-300"
                  />
                </div>
                <h3 className="text-xl md:text-2xl font-heading font-semibold text-neutral-900 mb-4">
                  {item.title}
                </h3>
                <p className="text-neutral-600 leading-relaxed text-base md:text-lg">
                  {item.description}
                </p>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
